# backend/app/utils/sdo_config.py
import json
import os
import re
from typing import Dict, Any
from ..models.press.config import SDOConfig
from .logger import get_logger

logger = get_logger(__name__)

class SDOConfigManager:
    """SDO配置管理类 - 负责解析和序列化SDO配置"""

    # 配置文件路径
    CONFIG_PATH = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config", "sdo_mapping.json")

    _mapping_cache: Dict[str, Any] = None

    @classmethod
    def load_mapping(cls) -> Dict[str, Any]:
        """
        加载SDO映射配置文件

        Returns:
            Dict[str, Any]: SDO映射配置

        Raises:
            Exception: 配置文件加载失败
        """
        if cls._mapping_cache is not None:
            return cls._mapping_cache

        try:
            logger.info(f"加载SDO映射配置文件: {cls.CONFIG_PATH}")

            if not os.path.exists(cls.CONFIG_PATH):
                raise FileNotFoundError(f"SDO映射配置文件不存在: {cls.CONFIG_PATH}")

            with open(cls.CONFIG_PATH, 'r', encoding='utf-8') as f:
                config = json.load(f)

            if 'sdo_mappings' not in config:
                raise ValueError("配置文件格式错误：缺少 'sdo_mappings' 字段")

            cls._mapping_cache = config['sdo_mappings']
            logger.info(f"SDO映射配置加载成功，包含 {len(cls._mapping_cache)} 个参数")

            return cls._mapping_cache

        except Exception as e:
            logger.error(f"加载SDO映射配置失败: {e}")
            raise e

    @classmethod
    def parse_sdo_content(cls, content: str) -> SDOConfig:
        """
        解析SDO内容为配置对象

        Args:
            content: SDO原始内容

        Returns:
            SDOConfig: 解析后的配置对象

        Raises:
            Exception: 解析失败
        """
        try:
            logger.info("开始解析SDO内容")

            if not content.strip():
                raise ValueError("SDO内容为空")

            # 加载映射配置
            mapping = cls.load_mapping()

            # 解析SDO内容为字典
            sdo_dict = cls._parse_sdo_lines(content)
            logger.debug(f"解析得到 {len(sdo_dict)} 个SDO参数")

            # 提取配置值
            config_values = {}
            for field_name, field_config in mapping.items():
                address = field_config['address'].upper()

                if address not in sdo_dict:
                    logger.warning(f"未找到SDO参数 {field_name} (地址: {address})")
                    # 设置默认值
                    config_values[field_name] = 0.0
                else:
                    value_str = sdo_dict[address]['value']
                    try:
                        config_values[field_name] = float(value_str)
                        logger.debug(f"提取参数 {field_name}: {config_values[field_name]}")
                    except ValueError as ve:
                        logger.error(f"参数 {field_name} 值转换失败: {value_str}")
                        raise ValueError(f"参数 {field_name} 值格式错误: {value_str}")

            # 创建配置对象
            config = SDOConfig(**config_values)
            logger.info("SDO内容解析成功")

            return config

        except Exception as e:
            logger.error(f"解析SDO内容失败: {e}")
            raise e

    @classmethod
    def serialize_sdo_config(cls, config: SDOConfig, original_content: str) -> str:
        """
        将配置对象序列化为SDO内容

        Args:
            config: 配置对象
            original_content: 原始SDO内容

        Returns:
            str: 序列化后的SDO内容

        Raises:
            Exception: 序列化失败
        """
        try:
            logger.info("开始序列化SDO配置")

            if not original_content.strip():
                raise ValueError("原始SDO内容为空")

            # 加载映射配置
            mapping = cls.load_mapping()

            # 分行处理
            lines = original_content.strip().split('\n')
            updated_lines = []

            # 构建更新映射表
            update_map = {}
            for field_name, field_config in mapping.items():
                address = field_config['address'].upper()
                value = getattr(config, field_name)
                update_map[address] = str(value)
                logger.debug(f"准备更新 {field_name} ({address}): {value}")

            # 逐行处理
            updated_count = 0
            for line in lines:
                line = line.strip()
                if not line:
                    updated_lines.append(line)
                    continue

                # 解析行格式: 地址,参数名,值
                parts = line.split(',', 2)
                if len(parts) != 3:
                    updated_lines.append(line)
                    continue

                address = parts[0].strip().upper()
                parameter_name = parts[1].strip()
                current_value = parts[2].strip()

                # 检查是否需要更新
                if address in update_map:
                    new_value = update_map[address]
                    if new_value != current_value:
                        updated_line = f"{address},{parameter_name},{new_value}"
                        updated_lines.append(updated_line)
                        updated_count += 1
                        logger.debug(f"更新参数 {address}: {current_value} -> {new_value}")
                    else:
                        updated_lines.append(line)
                else:
                    updated_lines.append(line)

            result = '\n'.join(updated_lines)
            logger.info(f"SDO配置序列化成功，更新了 {updated_count} 个参数")

            return result

        except Exception as e:
            logger.error(f"序列化SDO配置失败: {e}")
            raise e

    @classmethod
    def _parse_sdo_lines(cls, content: str) -> Dict[str, Dict[str, str]]:
        """
        解析SDO行内容为字典

        Args:
            content: SDO内容

        Returns:
            Dict[str, Dict[str, str]]: 解析结果 {地址: {name: 参数名, value: 值}}
        """
        sdo_dict = {}

        for line_num, line in enumerate(content.strip().split('\n'), 1):
            line = line.strip()
            if not line:
                continue

            try:
                # 解析格式: 地址,参数名,值
                parts = line.split(',', 2)
                if len(parts) != 3:
                    logger.debug(f"跳过格式不正确的行 {line_num}: {line}")
                    continue

                address = parts[0].strip().upper()
                parameter_name = parts[1].strip()
                value = parts[2].strip()

                sdo_dict[address] = {
                    'name': parameter_name,
                    'value': value
                }

            except Exception as e:
                logger.warning(f"解析第 {line_num} 行失败: {line} - {e}")
                continue

        return sdo_dict
