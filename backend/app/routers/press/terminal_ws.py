# backend/app/routers/press/terminal_ws.py
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException
from ...services.press_service.terminal_ws_service import terminal_manager
from ...services.auth_service import auth_service
from ...models.auth import FeatureType
from ...utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()

async def verify_terminal_access(session_id: str):
    """验证终端访问权限"""
    try:
        # 检查会话是否已授权访问终端功能
        auth_info = auth_service.get_authorization_info(session_id, FeatureType.TERMINAL)
        if not auth_info["authorized"]:
            raise HTTPException(status_code=403, detail="未授权访问终端功能")
        return True
    except Exception as e:
        logger.error(f"验证终端访问权限失败: {e}")
        raise HTTPException(status_code=500, detail="权限验证失败")

@router.websocket("/ws/{session_id}")
async def websocket_terminal(websocket: WebSocket, session_id: str):
    """WebSocket交互式终端端点"""
    logger.info(f"收到WebSocket终端连接请求: {session_id}")

    try:
        # 验证访问权限（在WebSocket握手前进行）
        try:
            await verify_terminal_access(session_id)
        except HTTPException as e:
            # WebSocket连接前的权限验证失败
            await websocket.close(code=4003, reason="Unauthorized")
            return

        # 建立WebSocket连接并启动终端
        await terminal_manager.connect_terminal(websocket, session_id)

        # 确保连接建立成功后再处理消息
        if terminal_manager.is_session_active(session_id):
            # 处理WebSocket消息循环
            await terminal_manager.handle_websocket_messages(websocket, session_id)
        else:
            logger.error(f"终端连接建立失败: {session_id}")

    except WebSocketDisconnect:
        logger.info(f"WebSocket终端连接断开: {session_id}")
    except Exception as e:
        logger.error(f"WebSocket终端处理异常: {e}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
    finally:
        # 确保资源清理
        if terminal_manager.is_session_active(session_id):
            await terminal_manager._cleanup_session(session_id)

@router.get("/status/{session_id}")
async def get_terminal_status(session_id: str):
    """获取终端状态"""
    try:
        is_active = terminal_manager.is_session_active(session_id)
        return {
            "success": True,
            "active": is_active,
            "message": "终端活跃" if is_active else "终端未连接"
        }
    except Exception as e:
        logger.error(f"获取终端状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取终端状态失败: {str(e)}")

@router.post("/disconnect/{session_id}")
async def disconnect_terminal(session_id: str):
    """强制断开终端连接"""
    try:
        if terminal_manager.is_session_active(session_id):
            await terminal_manager._cleanup_session(session_id)
            return {
                "success": True,
                "message": "终端连接已断开"
            }
        else:
            return {
                "success": True,
                "message": "终端未连接"
            }
    except Exception as e:
        logger.error(f"断开终端连接失败: {e}")
        raise HTTPException(status_code=500, detail=f"断开终端连接失败: {str(e)}")
