# backend/app/routers/press/__init__.py
"""
压机设备API路由模块

包含压机设备管理相关的所有API路由：
- file: 文件管理API
- network: 网络管理API
- upgrade: 升级管理API
- log: 日志管理API
- interactive_terminal: 交互式终端WebSocket API
- config: 压机配置API
"""

from fastapi import APIRouter
from .file import router as file_router
from .network import router as network_router
from .upgrade import router as upgrade_router
from .log import router as log_router
from .terminal import router as terminal_router
from .terminal_ws import router as terminal_ws_router
from .config import router as config_router

# 创建压机设备主路由
press_router = APIRouter(prefix="/api/press", tags=["压机设备"])

# 注册子路由
press_router.include_router(file_router, prefix="/file")
press_router.include_router(network_router, prefix="/network")
press_router.include_router(upgrade_router, prefix="/upgrade")
press_router.include_router(log_router, prefix="/log")
press_router.include_router(terminal_router, prefix="/terminal")
press_router.include_router(terminal_ws_router, prefix="/terminal")
press_router.include_router(config_router, prefix="/config")

__all__ = ["press_router"]
