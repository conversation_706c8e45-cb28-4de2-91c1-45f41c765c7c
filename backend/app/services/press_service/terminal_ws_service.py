# backend/app/services/press_service/terminal_ws_service.py
import asyncio
import json
from typing import Dict, Optional
from fastapi import WebSocket, WebSocketDisconnect
from ...utils.logger import get_logger
from ..ssh_service import session_manager

logger = get_logger(__name__)

class TerminalManager:
    """WebSocket终端管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接：session_id -> websocket
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储PTY进程：session_id -> process
        self.pty_processes: Dict[str, any] = {}
        # 默认终端尺寸：80列x24行（可动态调整）
        self.default_cols = 80
        self.default_rows = 24

    async def connect_terminal(self, websocket: WebSocket, session_id: str):
        """建立WebSocket连接并启动交互式终端"""
        try:
            await websocket.accept()
            logger.info(f"WebSocket连接请求: {session_id}")

            # 检查是否已有连接，如果有则先清理旧连接
            if session_id in self.active_connections:
                logger.info(f"发现已有连接，清理旧连接: {session_id}")
                await self._cleanup_session(session_id)

            # 检查SSH会话是否存在
            conn = session_manager.get_connection(session_id)
            if not conn:
                logger.error(f"SSH会话不存在: {session_id}")
                await self._send_message(websocket, "error", "SSH会话不存在或已断开")
                await websocket.close()
                return

            logger.info(f"SSH连接验证成功: {session_id}")

            # 保存连接
            self.active_connections[session_id] = websocket
            logger.info(f"WebSocket终端连接建立成功: {session_id}")

            # 启动PTY会话
            await self._start_pty_session(session_id, websocket, conn)

        except Exception as e:
            logger.error(f"WebSocket连接建立失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await self._cleanup_session(session_id)

    async def handle_websocket_messages(self, websocket: WebSocket, session_id: str):
        """处理WebSocket消息循环"""
        try:
            # 确保WebSocket连接有效
            if session_id not in self.active_connections:
                logger.error(f"WebSocket连接不存在: {session_id}")
                return

            while True:
                # 检查连接是否还有效
                if session_id not in self.active_connections:
                    logger.info(f"WebSocket连接已清理: {session_id}")
                    break

                # 接收来自前端的消息
                try:
                    message = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                    data = json.loads(message)

                    message_type = data.get("type")
                    logger.debug(f"收到WebSocket消息: session={session_id}, type={message_type}")

                    if message_type == "input":
                        # 用户输入
                        input_data = data.get("data", "")
                        logger.debug(f"收到用户输入: session={session_id}, data='{input_data}', len={len(input_data)}")
                        await self._handle_user_input(session_id, input_data)
                    elif message_type == "resize":
                        # 终端大小调整
                        cols = data.get("cols", self.default_cols)
                        rows = data.get("rows", self.default_rows)
                        logger.debug(f"收到终端大小调整请求: {cols}x{rows}")
                        await self._handle_terminal_resize(session_id, cols, rows)
                    elif message_type == "ping":
                        # 心跳检测
                        await self._send_message(websocket, "pong", "")
                    else:
                        logger.warning(f"未知消息类型: {message_type}")

                except asyncio.TimeoutError:
                    # 发送心跳检测
                    logger.debug(f"发送心跳检测: {session_id}")
                    try:
                        await self._send_message(websocket, "ping", "")
                    except Exception as e:
                        logger.error(f"发送心跳失败: {e}")
                        break
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {e}")
                    continue
                except Exception as e:
                    logger.error(f"接收消息失败: {e}")
                    break

        except WebSocketDisconnect:
            logger.info(f"WebSocket连接正常断开: {session_id}")
        except Exception as e:
            logger.error(f"WebSocket消息处理失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
        finally:
            # 不在这里清理，由路由层统一清理
            pass

    async def _start_pty_session(self, session_id: str, websocket: WebSocket, conn):
        """启动PTY会话"""
        try:
            # 创建交互式PTY进程
            logger.info(f"正在创建PTY进程: {session_id}")
            process = await conn.create_process(
                'bash -l',  # 启动登录shell
                term_type='xterm-256color',
                term_size=(self.default_cols, self.default_rows),
                encoding='utf-8'
            )
            
            # 等待一下确保进程启动
            await asyncio.sleep(0.1)
            
            # 检查进程是否立即退出
            if process.exit_status is not None:
                logger.error(f"PTY进程立即退出: {session_id}, exit_status: {process.exit_status}")
                await self._send_message(websocket, "error", f"终端进程启动失败，退出码: {process.exit_status}")
                return
            
            self.pty_processes[session_id] = process
            logger.info(f"PTY进程启动成功: {session_id}")
            
            # 发送连接成功消息
            await self._send_message(websocket, "connected", {
                "cols": self.default_cols,
                "rows": self.default_rows,
                "message": "控制台已连接"
            })
            
            # 启动输出监听任务（不等待，让它在后台运行）
            asyncio.create_task(self._handle_pty_output(session_id, process))
            
            # 启动进程退出监听任务（不等待，让它在后台运行）
            asyncio.create_task(self._monitor_process_exit(session_id, process))
                    
        except Exception as e:
            logger.error(f"启动PTY会话失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            await self._send_message(websocket, "error", f"启动终端失败: {str(e)}")

    async def _handle_pty_output(self, session_id: str, process):
        """处理PTY进程输出"""
        try:
            logger.info(f"开始监听PTY输出: {session_id}")
            while True:
                # 检查进程是否还存活
                if process.exit_status is not None:
                    logger.info(f"PTY进程已退出: {session_id}, exit_status: {process.exit_status}")
                    break
                    
                # 检查WebSocket连接是否还存在
                websocket = self.active_connections.get(session_id)
                if not websocket:
                    logger.info(f"WebSocket连接已断开，停止输出监听: {session_id}")
                    break
                
                # 读取stdout数据
                if process.stdout:
                    try:
                        chunk = await asyncio.wait_for(process.stdout.read(1024), timeout=1.0)
                        if chunk:
                            logger.debug(f"收到PTY输出: {session_id}, 长度: {len(chunk)}")
                            await self._send_message(websocket, "output", chunk)
                        else:
                            # stdout关闭，进程可能退出
                            logger.info(f"PTY进程stdout关闭: {session_id}")
                            break
                    except asyncio.TimeoutError:
                        # 没有数据可读，继续循环
                        continue
                    except Exception as e:
                        logger.error(f"读取PTY输出失败: {e}")
                        break
                else:
                    logger.warning(f"PTY进程没有stdout: {session_id}")
                    break
                    
        except asyncio.CancelledError:
            logger.info(f"PTY输出监听被取消: {session_id}")
        except Exception as e:
            logger.error(f"处理PTY输出失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
        finally:
            logger.info(f"PTY输出监听结束: {session_id}")

    async def _handle_user_input(self, session_id: str, input_data: str):
        """处理用户输入"""
        process = self.pty_processes.get(session_id)
        logger.debug(f"处理用户输入: session={session_id}, data_len={len(input_data)}, process_exists={process is not None}")
        
        if process and process.stdin and not process.stdin.is_closing():
            try:
                # 发送用户输入到PTY进程
                logger.debug(f"发送数据到PTY stdin")
                process.stdin.write(input_data)
                await process.stdin.drain()
                logger.debug(f"成功发送数据到PTY")
            except Exception as e:
                logger.error(f"发送用户输入失败: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
        else:
            logger.warning(f"无法发送用户输入: process_exists={process is not None}, stdin_available={process.stdin is not None if process else False}, stdin_closing={process.stdin.is_closing() if process and process.stdin else 'N/A'}")

    async def _handle_terminal_resize(self, session_id: str, cols: int, rows: int):
        """处理终端尺寸调整"""
        process = self.pty_processes.get(session_id)
        if process:
            try:
                # 调整PTY尺寸
                process.change_terminal_size(cols, rows)
                logger.info(f"终端尺寸已调整: {session_id}, {cols}x{rows}")
            except Exception as e:
                logger.error(f"调整终端尺寸失败: {e}")
        else:
            logger.warning(f"无法调整终端尺寸，进程不存在: {session_id}")

    async def _monitor_process_exit(self, session_id: str, process):
        """监听进程退出"""
        try:
            await process.wait()
            exit_code = process.exit_status
            logger.info(f"PTY进程退出: {session_id}, exit_code: {exit_code}")
            
            # 通知前端进程已退出
            websocket = self.active_connections.get(session_id)
            if websocket:
                await self._send_message(websocket, "exit", {
                    "exit_code": exit_code,
                    "message": "终端会话已结束"
                })
                
        except Exception as e:
            logger.error(f"监听进程退出失败: {e}")

    async def _send_message(self, websocket: WebSocket, message_type: str, data):
        """发送消息到WebSocket客户端"""
        try:
            message = {
                "type": message_type,
                "data": data
            }
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"发送WebSocket消息失败: {e}")

    async def _cleanup_session(self, session_id: str):
        """清理会话资源"""
        try:
            logger.info(f"开始清理会话资源: {session_id}")

            # 清理PTY进程
            process = self.pty_processes.pop(session_id, None)
            if process:
                try:
                    if not process.is_closing():
                        logger.info(f"终止PTY进程: {session_id}")
                        process.terminate()
                        await asyncio.wait_for(process.wait(), timeout=3.0)
                except asyncio.TimeoutError:
                    logger.warning(f"PTY进程终止超时，强制杀死: {session_id}")
                    process.kill()
                    try:
                        await process.wait_closed()
                    except Exception:
                        pass
                except Exception as e:
                    logger.error(f"清理PTY进程失败: {e}")

            # 清理WebSocket连接
            websocket = self.active_connections.pop(session_id, None)
            if websocket:
                try:
                    # 检查连接状态
                    if hasattr(websocket, 'client_state') and websocket.client_state.name != "DISCONNECTED":
                        logger.info(f"关闭WebSocket连接: {session_id}")
                        await websocket.close(code=1000, reason="Session cleanup")
                except Exception as e:
                    logger.error(f"关闭WebSocket连接失败: {e}")

            logger.info(f"会话资源清理完成: {session_id}")

        except Exception as e:
            logger.error(f"清理会话资源失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")

    def is_session_active(self, session_id: str) -> bool:
        """检查会话是否活跃"""
        return session_id in self.active_connections

# 全局终端管理器实例
terminal_manager = TerminalManager()
