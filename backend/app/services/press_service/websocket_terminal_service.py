# backend/app/services/press_service/websocket_terminal_service.py
import asyncio
import json
from typing import Dict, Optional
from fastapi import WebSocket, WebSocketDisconnect
from ...utils.logger import get_logger
from ..ssh_service import session_manager

logger = get_logger(__name__)

class WebSocketTerminalManager:
    """WebSocket交互式终端管理器"""
    
    def __init__(self):
        # 存储活跃的WebSocket连接：session_id -> websocket
        self.active_connections: Dict[str, WebSocket] = {}
        # 存储PTY进程：session_id -> process
        self.pty_processes: Dict[str, any] = {}
        # 终端尺寸：固定80x24
        self.terminal_size = (80, 24)

    async def connect(self, websocket: WebSocket, session_id: str):
        """建立WebSocket连接并启动PTY会话"""
        try:
            await websocket.accept()
            
            # 检查是否已有连接（单会话单连接）
            if session_id in self.active_connections:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "该会话已有活跃的终端连接"
                }))
                await websocket.close()
                return
            
            # 保存连接
            self.active_connections[session_id] = websocket
            logger.info(f"WebSocket终端连接建立: {session_id}")
            
            # 启动PTY会话
            await self._start_pty_session(session_id, websocket)
            
        except Exception as e:
            logger.error(f"WebSocket连接建立失败: {e}")
            await self._cleanup_connection(session_id)

    async def disconnect(self, session_id: str):
        """断开WebSocket连接并清理资源"""
        await self._cleanup_connection(session_id)
        logger.info(f"WebSocket终端连接断开: {session_id}")

    async def send_to_terminal(self, session_id: str, data: str):
        """向终端发送数据"""
        websocket = self.active_connections.get(session_id)
        if websocket:
            try:
                await websocket.send_text(json.dumps({
                    "type": "output",
                    "data": data
                }))
            except Exception as e:
                logger.error(f"发送数据到终端失败: {e}")
                await self._cleanup_connection(session_id)

    async def handle_input(self, session_id: str, data: str):
        """处理来自前端的输入数据"""
        process = self.pty_processes.get(session_id)
        if process and process.stdin:
            try:
                process.stdin.write(data)
                await process.stdin.drain()
            except Exception as e:
                logger.error(f"处理终端输入失败: {e}")

    async def _start_pty_session(self, session_id: str, websocket: WebSocket):
        """启动PTY会话"""
        try:
            # 获取SSH连接
            conn = session_manager.get_connection(session_id)
            if not conn:
                await websocket.send_text(json.dumps({
                    "type": "error", 
                    "message": "SSH会话不存在或已断开"
                }))
                return

            # 创建PTY进程（启动shell）
            cols, rows = self.terminal_size
            process = await conn.create_process(
                'bash -l',  # 启动登录shell
                term_type='xterm-256color',
                term_size=(cols, rows),
                encoding='utf-8',
                input=None
            )
            
            self.pty_processes[session_id] = process
            
            # 发送欢迎消息
            await websocket.send_text(json.dumps({
                "type": "connected",
                "message": f"交互式终端已连接 ({cols}x{rows})"
            }))
            
            # 启动输出监听任务
            asyncio.create_task(self._handle_process_output(session_id, process))
            
            # 监听进程退出
            asyncio.create_task(self._monitor_process_exit(session_id, process))
            
        except Exception as e:
            logger.error(f"启动PTY会话失败: {e}")
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"启动终端失败: {str(e)}"
            }))

    async def _handle_process_output(self, session_id: str, process):
        """处理PTY进程输出"""
        try:
            while True:
                # 读取stdout
                if process.stdout:
                    data = await process.stdout.read(8192)
                    if data:
                        await self.send_to_terminal(session_id, data)
                    else:
                        # stdout关闭
                        break
                        
                # 检查进程是否还活着
                if process.exit_status is not None:
                    break
                    
        except asyncio.CancelledError:
            logger.info(f"进程输出监听被取消: {session_id}")
        except Exception as e:
            logger.error(f"处理进程输出失败: {e}")
        finally:
            await self._cleanup_connection(session_id)

    async def _monitor_process_exit(self, session_id: str, process):
        """监听进程退出"""
        try:
            await process.wait()
            logger.info(f"PTY进程退出: {session_id}, exit_status: {process.exit_status}")
            
            # 通知前端进程已退出
            websocket = self.active_connections.get(session_id)
            if websocket:
                await websocket.send_text(json.dumps({
                    "type": "exit",
                    "exit_code": process.exit_status,
                    "message": "终端会话已结束"
                }))
                
        except Exception as e:
            logger.error(f"监听进程退出失败: {e}")
        finally:
            await self._cleanup_connection(session_id)

    async def _cleanup_connection(self, session_id: str):
        """清理连接和资源"""
        try:
            # 清理PTY进程
            process = self.pty_processes.pop(session_id, None)
            if process:
                try:
                    if not process.is_closing():
                        process.terminate()
                        await asyncio.wait_for(process.wait(), timeout=5.0)
                except asyncio.TimeoutError:
                    process.kill()
                except Exception as e:
                    logger.error(f"清理PTY进程失败: {e}")
            
            # 清理WebSocket连接
            websocket = self.active_connections.pop(session_id, None)
            if websocket:
                try:
                    if not websocket.client_state.value == 3:  # 3 = CLOSED
                        await websocket.close()
                except Exception as e:
                    logger.error(f"关闭WebSocket连接失败: {e}")
                    
        except Exception as e:
            logger.error(f"清理连接资源失败: {e}")

# 全局终端管理器实例
terminal_manager = WebSocketTerminalManager()