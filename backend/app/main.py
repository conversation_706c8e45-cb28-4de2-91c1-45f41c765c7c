# backend/app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path
import sys

from .routers import ssh
from .routers.press import press_router
from .routers.press.interactive_terminal import router as interactive_terminal_router
from .routers import auth
from .utils.logger import setup_logger, get_logger

# ─────────────────── 日志系统 ─────────────────── #
setup_logger()
logger = get_logger(__name__)

# ─────────────────── 工具函数 ─────────────────── #
def get_frontend_dist() -> Path:
    """
    返回 React build 目录:

    • 开发环境: <repo_root>/frontend/dist
    • 打包后  : <sys._MEIPASS>/frontend/dist
    """
    try:
        base = Path(sys._MEIPASS)  # PyInstaller 解压后的临时目录
    except AttributeError:
        base = Path(__file__).resolve().parent.parent.parent  # 源代码仓库根
    return base / "frontend" / "dist"

# ─────────────────── FastAPI 实例 ─────────────────── #
app = FastAPI(
    title="ControllerTool API",
    version="1.0.0",
    description="压机/拧紧设备远程管理系统 API",
    docs_url="/docs",
    redoc_url="/redoc"
)

# ─────────────────── 中间件 ─────────────────── #
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",
        "http://127.0.0.1:5173",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ],
    allow_methods=["*"],
    allow_headers=["*"],
    allow_credentials=True,
)

# ─────────────────── 路由注册 ─────────────────── #
app.include_router(ssh.router,   prefix="/api")
app.include_router(press_router)          # 内部已含 prefix
app.include_router(interactive_terminal_router, prefix="/api/press/terminal")  # 交互式终端WebSocket路由
app.include_router(auth.router)

# ─────────────────── 生命周期事件 ─────────────────── #
@app.on_event("startup")
async def startup_event():
    logger.info("ControllerTool API 启动成功")
    logger.info("Swagger 文档: http://127.0.0.1:8000/docs")
    logger.info("ReDoc   文档: http://127.0.0.1:8000/redoc")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("ControllerTool API 正在关闭…")

# ─────────────────── 调试接口 ─────────────────── #
@app.get("/api/info")
async def api_info():
    """系统信息 (供调试)"""
    return {
        "name": "ControllerTool API",
        "version": "1.0.0",
        "description": "压机/拧紧设备远程管理系统",
        "docs": "/docs",
        "status": "running",
    }

@app.get("/api/health")
async def health():
    return {"status": "healthy", "service": "ControllerTool API", "version": "1.0.0"}

# ─────────────────── 前端静态文件挂载 ─────────────────── #
FRONTEND_DIST = get_frontend_dist()

if FRONTEND_DIST.exists() and (FRONTEND_DIST / "index.html").exists():
    # html=True 会把未知路径回退到 index.html（解决前端路由刷新 404）
    app.mount(
        "/",                                     # 根路径
        StaticFiles(directory=str(FRONTEND_DIST), html=True),
        name="frontend",
    )
    logger.info(f"✅ 前端静态文件已挂载: {FRONTEND_DIST}")
else:
    logger.warning(f"⚠️  未找到前端静态文件目录: {FRONTEND_DIST}")
    logger.warning("⚠️  仅启动 API 服务")

# ─────────────────── 作为脚本运行 ─────────────────── #
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)

