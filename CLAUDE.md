# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 技术栈

### 后端
- **Python 3.12+** with FastAPI framework
- **依赖管理**: uv (pyproject.toml)
- **主要依赖**: asyncssh, aiosqlite, uvicorn
- **启动方式**: `python launcher.py` (生产) 或 `python -m app.main` (开发)

### 前端  
- **React 19** + **TypeScript 5.8** + **Vite 7**
- **UI框架**: Tailwind CSS v4
- **路由**: React Router DOM v7
- **构建命令**: `npm run build`, **开发命令**: `npm run dev`
- **代码检查**: `npm run lint`

## 代码结构

### 后端架构 (`backend/`)
```
app/
├── main.py              # FastAPI应用入口，路由注册
├── models/              # Pydantic数据模型
│   ├── press/           # 压机设备相关模型
│   └── ssh.py           # SSH连接模型
├── routers/             # API路由层
├── services/            # 业务逻辑层
│   ├── ssh_service.py   # SSH会话管理
│   └── press_service/   # 压机功能模块
└── utils/               # 工具函数
    ├── database.py
    ├── logger.py
    └── sdo_config.py
```

### 前端架构 (`frontend/`)
```
src/
├── App.tsx              # 路由配置
├── pages/
│   ├── ConnectionPage.tsx     # SSH连接界面
│   └── PressManagementPage.tsx # 设备管理界面
├── components/
│   ├── press/           # 压机管理组件
│   └── shared/          # 公共组件
└── utils/
    └── session.ts       # 会话管理
```

## 开发规范

### 架构原则
- **分层架构**: Models → Routers → Services → Utils
- **模块化设计**: 按功能领域划分服务模块
- **动态配置**: 使用JSON配置文件动态生成Pydantic模型

### 代码规范
- **后端**: 使用类型注解，遵循FastAPI约定
- **前端**: 严格TypeScript类型检查，函数式组件
- **日志**: 使用`utils/logger.py`统一日志管理
- **错误处理**: API统一返回格式，前端统一错误提示

### 关键组件
- **SSH连接管理**: `services/ssh_service.py` - 会话池管理
- **进程控制**: `services/press_service/process_service.py` - rtPress进程管理  
- **动态配置**: `models/press/config.py` - 基于JSON动态生成模型