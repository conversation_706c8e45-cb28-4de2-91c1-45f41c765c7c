import { useState, useEffect, useRef } from "react";
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';

// 会话信息类型
interface SessionInfo {
  sessionId: string;
  host: string;
  username: string;
  productLine: "press" | "tighten";
  connectedAt: string;
}

interface TerminalProps {
  sessionInfo: SessionInfo;
}

export default function Terminal({ sessionInfo }: TerminalProps) {
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  // 检查当前会话是否已授权
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch(`http://127.0.0.1:8000/api/auth/status/${sessionInfo.sessionId}/terminal`);
      const data = await response.json();
      setIsAuthorized(data.authorized);
    } catch (error) {
      console.error('检查权限失败:', error);
      setIsAuthorized(false);
    } finally {
      setIsChecking(false);
    }
  };

  const handleAuthSuccess = () => {
    setIsAuthorized(true);
  };

  if (isChecking) {
    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
        <div className="text-center py-8">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">检查控制台访问权限...</p>
        </div>
      </div>
    );
  }

  if (!isAuthorized) {
    return <TerminalAuthGuard sessionInfo={sessionInfo} onSuccess={handleAuthSuccess} />;
  }

  return <InteractiveTerminal sessionInfo={sessionInfo} />;
}

// 终端权限验证组件
function TerminalAuthGuard({ sessionInfo, onSuccess }: { sessionInfo: SessionInfo; onSuccess: () => void }) {
  const [passcode, setPasscode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState('');
  const [currentPasscode, setCurrentPasscode] = useState('');
  const [remainingTime, setRemainingTime] = useState(0);

  // 获取当前口令信息（仅开发环境显示）
  const fetchCurrentPasscode = async () => {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/auth/passcode/current');
      const data = await response.json();
      if (data.success) {
        setCurrentPasscode(data.data.passcode);
        setRemainingTime(data.data.remaining_seconds);
      }
    } catch (error) {
      console.error('获取口令失败:', error);
    }
  };

  // 验证口令
  const handleVerify = async () => {
    if (passcode.length < 6) {
      setError('请输入有效口令');
      return;
    }

    setIsVerifying(true);
    setError('');

    try {
      const response = await fetch('http://127.0.0.1:8000/api/auth/verify', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionInfo.sessionId,
          passcode: passcode,
          feature: "terminal"
        })
      });

      if (response.ok) {
        onSuccess();
      } else {
        const data = await response.json();
        setError(data.detail || '口令错误或已过期');
      }
    } catch (error) {
      setError('网络错误，请重试');
    } finally {
      setIsVerifying(false);
    }
  };

  // 倒计时更新
  useEffect(() => {
    if (remainingTime > 0) {
      const timer = setInterval(() => {
        setRemainingTime(prev => {
          if (prev <= 1) {
            fetchCurrentPasscode(); // 重新获取新口令
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [remainingTime]);

  useEffect(() => {
    fetchCurrentPasscode();
    setPasscode('');
    setError('');
  }, []);

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="text-center mb-8">
        <div className="text-6xl mb-4">🔐</div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">控制台访问验证</h2>
        <p className="text-gray-600">此功能需要管理员权限，请输入动态口令</p>
      </div>

      {/* 调试信息 - 开发环境显示当前口令 */}
      {import.meta.env.DEV && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
          <div className="text-sm text-yellow-800">
            🔑 当前口令: <code className="font-mono font-bold text-lg">{currentPasscode}</code>
          </div>
          <div className="text-xs text-yellow-600">
            {Math.floor(remainingTime / 60)}:{(remainingTime % 60).toString().padStart(2, '0')} 后更换
          </div>
        </div>
      )}

      <div className="max-w-sm mx-auto space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            请输入6位动态口令
          </label>
          <input
            type="text"
            value={passcode}
            onChange={(e) => setPasscode(e.target.value.slice(0, 20))}
            onKeyPress={(e) => e.key === 'Enter' && handleVerify()}
            className="w-full px-6 py-4 text-center text-xl font-mono tracking-wide rounded-xl border-2 border-gray-200 focus:border-blue-500 focus:outline-none"
            placeholder="123456"
            maxLength={20}
          />
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <p className="text-red-700 text-sm text-center">{error}</p>
          </div>
        )}

        <button
          onClick={handleVerify}
          disabled={passcode.length < 6 || isVerifying}
          className="w-full py-3 bg-blue-500 text-white rounded-xl hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-lg transition-colors"
        >
          {isVerifying ? '验证中...' : '验证权限'}
        </button>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            💡 口令每3分钟更换，本次连接期间有效
          </p>
        </div>
      </div>
    </div>
  );
}

// 控制台组件
function InteractiveTerminal({ sessionInfo }: { sessionInfo: SessionInfo }) {
  const terminalRef = useRef<HTMLDivElement>(null);
  const resizeHandlerRef = useRef<(() => void) | null>(null);
  const [terminal, setTerminal] = useState<XTerm | null>(null);
  const [websocket, setWebsocket] = useState<WebSocket | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'error'>('disconnected');
  const [errorMessage, setErrorMessage] = useState('');

  useEffect(() => {
    if (terminalRef.current && !terminal) {
      initializeTerminal();
    }

    return () => {
      cleanup();
    };
  }, []);

  // 处理终端大小调整
  useEffect(() => {
    if (terminal && websocket && websocket.readyState === WebSocket.OPEN) {
      const handleTerminalResize = () => {
        const message = {
          type: 'resize',
          cols: terminal.cols,
          rows: terminal.rows
        };
        websocket.send(JSON.stringify(message));
      };

      // 监听终端大小变化
      terminal.onResize(handleTerminalResize);
    }
  }, [terminal, websocket]);

  const initializeTerminal = () => {
    if (!terminalRef.current) return;

    // 创建终端实例
    const term = new XTerm({
      cursorBlink: true,
      theme: {
        background: '#1a1a1a',
        foreground: '#ffffff',
        cursor: '#ffffff'
      },
      fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
      fontSize: 14,
      lineHeight: 1.2
    });

    // 添加插件
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();

    term.loadAddon(fitAddon);
    term.loadAddon(webLinksAddon);

    // 打开终端
    term.open(terminalRef.current);

    // 自适应大小
    fitAddon.fit();

    // 监听窗口大小变化
    const handleResize = () => {
      fitAddon.fit();
    };
    resizeHandlerRef.current = handleResize;
    window.addEventListener('resize', handleResize);

    // 让终端获得焦点
    term.focus();

    setTerminal(term);

    // 连接WebSocket
    connectWebSocket(term, fitAddon);
  };

  const connectWebSocket = (term: XTerm, fitAddon?: FitAddon) => {
    setConnectionStatus('connecting');
    setErrorMessage('');

    const wsUrl = `ws://127.0.0.1:8000/api/press/terminal/ws/${sessionInfo.sessionId}`;
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket连接已建立');
      setConnectionStatus('connected');
      
      // 发送连接成功的欢迎信息
      term.writeln('\x1b[32m✓ 交互式终端连接成功\x1b[0m');
      term.writeln('正在启动终端会话...\r\n');
      
      // 确保终端获得焦点
      setTimeout(() => term.focus(), 100);
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(term, message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    };

    ws.onclose = (event) => {
      console.log('WebSocket连接已关闭:', event.code, event.reason);
      setConnectionStatus('disconnected');
      
      if (event.code === 4003) {
        setErrorMessage('未授权访问终端功能');
      } else {
        term.writeln('\r\n\x1b[31m✗ 终端连接已断开\x1b[0m');
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket连接错误:', error);
      setConnectionStatus('error');
      setErrorMessage('终端连接错误');
    };

    // 处理用户输入
    term.onData((data) => {
      console.log('终端输入数据:', data, '长度:', data.length, 'WebSocket状态:', ws.readyState);
      if (ws.readyState === WebSocket.OPEN) {
        const message = {
          type: 'input',
          data: data
        };
        console.log('发送WebSocket消息:', message);
        ws.send(JSON.stringify(message));
      } else {
        console.warn('WebSocket未连接，无法发送输入');
      }
    });

    setWebsocket(ws);
  };

  const handleWebSocketMessage = (term: XTerm, message: any) => {
    switch (message.type) {
      case 'connected':
        term.writeln(`\x1b[32m终端已连接 (${message.data.cols}x${message.data.rows})\x1b[0m\r`);
        // 确保终端获得焦点，准备接收输入
        setTimeout(() => term.focus(), 200);
        break;
        
      case 'output':
        term.write(message.data);
        break;
        
      case 'exit':
        term.writeln(`\r\n\x1b[33m进程已退出 (退出码: ${message.data.exit_code})\x1b[0m`);
        setConnectionStatus('disconnected');
        break;
        
      case 'error':
        term.writeln(`\r\n\x1b[31m错误: ${message.data}\x1b[0m`);
        setConnectionStatus('error');
        setErrorMessage(message.data);
        break;
        
      case 'pong':
        // 心跳响应，忽略
        console.log('收到心跳响应');
        break;
        
      case 'ping':
        // 服务器心跳检测，回复pong
        console.log('收到服务器心跳，回复pong');
        const currentWs = websocket;
        if (currentWs && currentWs.readyState === WebSocket.OPEN) {
          currentWs.send(JSON.stringify({ type: 'pong', data: '' }));
        }
        break;
        
      default:
        console.warn('未知消息类型:', message.type);
    }
  };

  const reconnect = () => {
    cleanup();
    setTimeout(() => {
      initializeTerminal();
    }, 1000);
  };

  const cleanup = () => {
    // 移除窗口resize监听器
    if (resizeHandlerRef.current) {
      window.removeEventListener('resize', resizeHandlerRef.current);
      resizeHandlerRef.current = null;
    }

    if (websocket) {
      websocket.close();
      setWebsocket(null);
    }
    if (terminal) {
      terminal.dispose();
      setTerminal(null);
    }
    setConnectionStatus('disconnected');
    setErrorMessage('');
  };

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-100 text-green-700';
      case 'connecting': return 'bg-yellow-100 text-yellow-700';
      case 'error': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return '已连接';
      case 'connecting': return '连接中...';
      case 'error': return '连接错误';
      default: return '未连接';
    }
  };

  return (
    <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 p-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center gap-2">
          🖥️ 控制台
          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </h2>
        <div className="flex gap-3">
          {connectionStatus !== 'connected' && (
            <button
              onClick={reconnect}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              重新连接
            </button>
          )}
          <button
            onClick={cleanup}
            className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
          >
            断开连接
          </button>
        </div>
      </div>

      {errorMessage && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">⚠️ {errorMessage}</p>
        </div>
      )}

      {/* 终端容器 */}
      <div
        className="bg-black rounded-xl p-4 h-[600px] w-full"
        onClick={() => terminal?.focus()}
      >
        <div ref={terminalRef} className="terminal-container w-full h-full" />
      </div>
    </div>
  );
}