import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  build: {
    chunkSizeWarningLimit: 1000, // 提高警告阈值到1MB
    rollupOptions: {
      output: {
        manualChunks: {
          // 将xterm.js相关库打包到单独的chunk
          'xterm': ['@xterm/xterm', '@xterm/addon-fit', '@xterm/addon-web-links'],
          // React相关库
          'react-vendor': ['react', 'react-dom'],
          // 路由库
          'router': ['react-router-dom']
        }
      }
    }
  }
})
